
// @ts-ignore - Deno imports work in Supabase Edge Functions
import "https://deno.land/x/xhr@0.1.0/mod.ts";
// @ts-ignore - Deno imports work in Supabase Edge Functions
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Helper function to create profile summary for AI context
const createProfileSummary = (profile: any): string => {
  console.log('Creating profile summary for:', profile);
  if (!profile || !profile.personalInfo) {
    return "No profile information collected yet";
  }

  const info = profile.personalInfo;
  const parts: string[] = [];

  if (info.fullName) parts.push(`Name: ${info.fullName}`);
  if (info.email) parts.push(`Email: ${info.email}`);
  if (info.phone) parts.push(`Phone: ${info.phone}`);
  if (info.address) parts.push(`Address: ${info.address}`);
  if (info.nationality) parts.push(`Nationality: ${info.nationality}`);

  if (profile.workExperience && profile.workExperience.length > 0) {
    parts.push(`Work Experience: ${profile.workExperience.length} job(s) recorded`);
  }

  if (profile.skills && profile.skills.length > 0) {
    parts.push(`Skills: ${profile.skills.length} skill(s) recorded`);
  }

  if (profile.languages && profile.languages.length > 0) {
    parts.push(`Languages: ${profile.languages.length} language(s) recorded`);
  }

  return parts.length > 0 ? parts.join(", ") : "No profile information collected yet";
};

// Helper function to create conversation context
const createConversationContext = (state: string, profile: any): string => {
  const info = profile?.personalInfo || {};
  console.log('Creating conversation context for state:', state, 'with profile info:', info);

  switch (state) {
    case 'role_selection':
      return "Ask what type of hospitality job they want (waiter, chef, bartender, etc.)";

    case 'personal_info':
      const missing: string[] = [];
      if (!info.fullName) missing.push("full name");
      if (!info.email) missing.push("email address");
      if (!info.phone) missing.push("phone number");

      if (missing.length > 0) {
        return `Still need to collect: ${missing.join(", ")}. Ask for the FIRST missing item only.`;
      }
      return "All personal info collected - move to address/nationality";

    case 'contact_details':
      const missingContact: string[] = [];
      if (!info.address) missingContact.push("address in Germany");
      if (!info.nationality) missingContact.push("nationality");

      if (missingContact.length > 0) {
        return `Still need to collect: ${missingContact.join(", ")}. Ask for the FIRST missing item only.`;
      }
      return "All contact details collected - move to work experience";

    case 'work_experience':
      return "Collect details about their previous hospitality jobs (job title, company, duration, responsibilities)";

    case 'skills':
      return "Collect their relevant skills for hospitality work";

    case 'languages':
      return "Collect languages they speak and proficiency levels";

    case 'education':
      return "Collect their educational background";

    case 'certifications':
      return "Collect any relevant certificates or training";

    case 'completion':
      return "Congratulate them - their CV is complete!";

    default:
      return "Continue collecting CV information";
  }
};

// Helper function to create conversation history context
const createHistoryContext = (history: Array<{content: string, isUser: boolean}>): string => {
  if (!history || history.length === 0) {
    return "This is the start of the conversation";
  }

  // Get last 4 exchanges (2 user + 2 assistant messages)
  const recentHistory = history.slice(-4);
  const formattedHistory = recentHistory.map(msg =>
    `${msg.isUser ? 'User' : 'Assistant'}: ${msg.content}`
  ).join('\n');

  return `Recent conversation:\n${formattedHistory}`;
};

// Local response handler to prevent duplicate questions
const handleLocalResponse = (userInput: string, conversationState: string, currentProfile: any): any => {
  // More flexible local handling for critical validations only
  // Let AI handle most responses for better conversation flow

  if (conversationState === 'personal_info') {
    if (!currentProfile?.personalInfo?.fullName) {
      // More flexible name validation
      const nameWords = userInput.trim().split(/\s+/).filter((w: string) => w.length > 0);
      if (nameWords.length >= 2 && nameWords.every((word: string) => /^[a-zA-ZÀ-ÿ\u0100-\u017F\u0180-\u024F\u1E00-\u1EFF]+$/.test(word))) {
        const formattedName = nameWords.map((word: string) =>
          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        ).join(' ');
        return {
          message: `Perfect! Hello ${formattedName}! Now I need your email address so potential employers can contact you.`
        };
      }
      // Let AI handle invalid names for better conversation
      return null;
    }

    if (!currentProfile?.personalInfo?.email) {
      // More flexible email validation
      const emailMatch = userInput.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
      if (emailMatch) {
        return {
          message: `Great! I have your email as ${emailMatch[0]}. Now I need your phone number so employers can call you directly.`
        };
      }
      // Let AI handle invalid emails for better conversation
      return null;
    }

    if (!currentProfile?.personalInfo?.phone) {
      // More flexible phone validation - use same regex as extractUserProfile
      const phoneMatch = userInput.match(/[\+]?[(]?[\d\s\-\(\)\.]{8,}/);
      if (phoneMatch) {
        // Check if we have email already - if not, this shouldn't happen in proper flow
        if (!currentProfile?.personalInfo?.email) {
          return {
            message: `Great! I have your phone number. Now I need your email address so employers can contact you.`
          };
        } else {
          return {
            message: `Perfect! Now I need your address in Germany. Please tell me your street address, city, and postal code.`
          };
        }
      }
      // Let AI handle invalid phones for better conversation
      return null;
    }

    if (!currentProfile?.personalInfo?.address) {
      // More flexible address validation - check for street name, number, and city
      const hasStreetAndNumber = /\d+/.test(userInput) && userInput.length > 10;
      const hasGermanCity = /berlin|hamburg|münchen|munich|köln|cologne|frankfurt|stuttgart|düsseldorf|dortmund|essen|leipzig|bremen|dresden|hannover|nürnberg|nuremberg/i.test(userInput);
      const hasPostalCode = /\d{5}/.test(userInput);

      if ((hasStreetAndNumber || hasGermanCity || hasPostalCode) && userInput.length > 15) {
        return {
          message: `Great! I have your address. Now I need to know your nationality. What country are you from?`
        };
      }
      // Let AI handle invalid addresses for better conversation
      return null;
    }
  }

  // Only handle nationality if it's clearly identifiable
  if (conversationState === 'contact_details' && !currentProfile?.personalInfo?.nationality) {
    const nationalities: Record<string, string> = {
      'china': 'Chinese', 'chinese': 'Chinese',
      'german': 'German', 'germany': 'German', 'deutschland': 'German',
      'american': 'American', 'usa': 'American', 'united states': 'American',
      'british': 'British', 'uk': 'British', 'england': 'British',
      'spanish': 'Spanish', 'spain': 'Spanish',
      'french': 'French', 'france': 'French',
      'italian': 'Italian', 'italy': 'Italian',
      'turkish': 'Turkish', 'turkey': 'Turkish',
      'poland': 'Polish', 'polish': 'Polish',
      'ukrainian': 'Ukrainian', 'ukraine': 'Ukrainian',
      'syrian': 'Syrian', 'syria': 'Syrian',
      'afghan': 'Afghan', 'afghanistan': 'Afghan',
      'romanian': 'Romanian', 'romania': 'Romanian',
      'bulgaria': 'Bulgarian', 'bulgarian': 'Bulgarian',
      'indian': 'Indian', 'india': 'Indian',
      'pakistani': 'Pakistani', 'pakistan': 'Pakistani'
    };

    const inputLower = userInput.toLowerCase();
    const foundKey = Object.keys(nationalities).find(key => inputLower.includes(key));
    if (foundKey) {
      return {
        message: `Great! Now let's talk about your work experience. Tell me about your previous jobs in hotels, restaurants, or bars. What was your role and where did you work?`
      };
    }
  }

  // Let AI handle everything else for more natural conversation
  return null;
};

// Extract user profile from input
const extractUserProfile = (userInput: string, state: string, currentProfile: any): any => {
  const profile: any = {
    personalInfo: {},
    workExperience: [],
    skills: [],
    languages: [],
    education: [],
    certifications: []
  };

  console.log('Extracting profile for state:', state, 'Input:', userInput);

  switch (state) {
    case 'personal_info':
      // Extract name if not already collected and present in input
      if (!currentProfile?.personalInfo?.fullName) {
        const nameWords = userInput.trim().split(' ').filter((w: string) => w.length > 1);
        if (nameWords.length >= 2) {
          // Capitalize first letter of each word
          const formattedName = nameWords.map((word: string) =>
            word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          ).join(' ');
          profile.personalInfo.fullName = formattedName;
        }
      }

      // Extract email if not already collected and present in input
      if (!currentProfile?.personalInfo?.email) {
        const emailMatch = userInput.match(/\S+@\S+\.\S+/);
        if (emailMatch) {
          profile.personalInfo.email = emailMatch[0];
        }
      }

      // Extract phone if not already collected and present in input
      if (!currentProfile?.personalInfo?.phone) {
        const phoneMatch = userInput.match(/[\+]?[(]?[\d\s\-\(\)\.]{8,}/);
        if (phoneMatch) {
          profile.personalInfo.phone = phoneMatch[0].trim();
        }
      }
      break;

    case 'contact_details':
      if (!currentProfile?.personalInfo?.address) {
        // More flexible address validation - check for street name, number, and city
        const hasStreetAndNumber = /\d+/.test(userInput) && userInput.length > 10;
        const hasGermanCity = /berlin|hamburg|münchen|munich|köln|cologne|frankfurt|stuttgart|düsseldorf|dortmund|essen|leipzig|bremen|dresden|hannover|nürnberg|nuremberg/i.test(userInput);
        const hasPostalCode = /\d{5}/.test(userInput);
        const hasStreetKeywords = /str|straße|platz|weg|allee|ring|damm|ufer/i.test(userInput);

        if ((hasStreetAndNumber || hasGermanCity || hasPostalCode || hasStreetKeywords) && userInput.length > 15) {
          profile.personalInfo.address = userInput.trim();
        }
      } else if (!currentProfile?.personalInfo?.nationality) {
        const nationalities: Record<string, string> = {
          'china': 'Chinese', 'chinese': 'Chinese', 'german': 'German', 'germany': 'German', 'deutschland': 'German',
          'american': 'American', 'usa': 'American', 'united states': 'American',
          'british': 'British', 'uk': 'British', 'england': 'British',
          'spanish': 'Spanish', 'spain': 'Spanish',
          'french': 'French', 'france': 'French',
          'italian': 'Italian', 'italy': 'Italian',
          'turkish': 'Turkish', 'turkey': 'Turkish',
          'polish': 'Polish', 'poland': 'Polish',
          'ukrainian': 'Ukrainian', 'ukraine': 'Ukrainian',
          'syrian': 'Syrian', 'syria': 'Syrian',
          'afghan': 'Afghan', 'afghanistan': 'Afghan',
          'romanian': 'Romanian', 'romania': 'Romanian',
          'bulgarian': 'Bulgarian', 'bulgaria': 'Bulgarian',
          'indian': 'Indian', 'india': 'Indian',
          'pakistani': 'Pakistani', 'pakistan': 'Pakistani'
        };

        const inputLower = userInput.toLowerCase();
        const foundKey = Object.keys(nationalities).find((key: string) => inputLower.includes(key));
        if (foundKey) {
          profile.personalInfo.nationality = nationalities[foundKey];
        }
      }
      break;
  }

  console.log('Extracted profile:', profile);
  console.log('Current profile before merge:', currentProfile);
  return profile;
};

// Get next conversation state
const getNextConversationState = (currentState: string, extractedProfile: any, currentProfile: any): string => {
  console.log('=== getNextConversationState ===');
  console.log('currentState:', currentState);
  console.log('extractedProfile:', extractedProfile);
  console.log('currentProfile:', currentProfile);

  if (currentState === 'role_selection') {
    console.log('Moving from role_selection to personal_info');
    return 'personal_info';
  }

  if (currentState === 'personal_info') {
    const personalInfo = { ...currentProfile?.personalInfo, ...extractedProfile?.personalInfo };
    console.log('Merged personalInfo:', personalInfo);
    console.log('Checking completeness - fullName:', !!personalInfo.fullName, 'email:', !!personalInfo.email, 'phone:', !!personalInfo.phone);

    if (personalInfo.fullName && personalInfo.email && personalInfo.phone) {
      console.log('All personal info complete, moving to contact_details');
      return 'contact_details';
    }
    console.log('Personal info incomplete, staying in personal_info');
    return 'personal_info';
  }

  if (currentState === 'contact_details') {
    const personalInfo = { ...currentProfile?.personalInfo, ...extractedProfile?.personalInfo };
    console.log('Contact details check - address:', !!personalInfo.address, 'nationality:', !!personalInfo.nationality);

    if (personalInfo.address && personalInfo.nationality) {
      console.log('Contact details complete, moving to work_experience');
      return 'work_experience';
    }
    console.log('Contact details incomplete, staying in contact_details');
    return 'contact_details';
  }

  console.log('No state change, returning:', currentState);
  return currentState;
};

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { userInput, conversationState, currentProfile, conversationHistory } = await req.json();

    console.log('=== NEW REQUEST ===');
    console.log('userInput:', userInput);
    console.log('conversationState:', conversationState);
    console.log('currentProfile:', JSON.stringify(currentProfile, null, 2));
    console.log('conversationHistory length:', conversationHistory?.length || 0);

    // @ts-ignore - Deno global is available in Supabase Edge Functions
    const apiKey = Deno.env.get('GEMINI_API_KEY');
    if (!apiKey) {
      throw new Error('Gemini API key not configured');
    }

    // First check if we should handle this locally to prevent duplicate questions
    const localResponse = handleLocalResponse(userInput, conversationState, currentProfile);
    if (localResponse) {
      // Extract user profile from the input even for local responses
      const extractedProfile = extractUserProfile(userInput, conversationState, currentProfile);
      return new Response(JSON.stringify({
        message: localResponse.message,
        userProfile: extractedProfile,
        nextState: getNextConversationState(conversationState, extractedProfile, currentProfile)
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Create comprehensive context-aware prompts
    const profileSummary = createProfileSummary(currentProfile);
    const conversationContext = createConversationContext(conversationState, currentProfile);
    const historyContext = createHistoryContext(conversationHistory || []);

    const systemPrompt = `You are an experienced career counselor specializing in helping hospitality workers in Germany find jobs. You are warm, encouraging, and professional.

CONTEXT:
- Current conversation stage: ${conversationState}
- User's current profile: ${profileSummary}
- What you need to collect: ${conversationContext}
- Recent conversation: ${historyContext}

PERSONALITY & STYLE:
- Be warm, encouraging, and supportive
- Use simple, clear language appropriate for hospitality workers
- Show genuine interest in their career goals
- Be patient and understanding with non-native speakers
- Never mention you are an AI or refer to technical terms

CONVERSATION RULES:
- Ask only ONE question at a time
- Build on what the user just said - acknowledge their input
- Reference previous conversation when relevant
- NEVER ask for information you already have in the profile
- If user gives incomplete info, ask for clarification gently
- If user goes off-topic, redirect kindly but firmly
- Validate and encourage good responses
- If user seems confused, rephrase your question differently

IMPORTANT: Look at the profile summary above. If information is already collected (like name, email, phone), DO NOT ask for it again. Move to the next missing piece of information.

CURRENT USER INPUT: "${userInput}"

Respond naturally as a helpful career counselor would, acknowledging what they said and asking the next appropriate question based on your conversation stage and what information is still missing.`;

    // Try multiple models with retry logic
    const models = ['gemini-2.0-flash-exp', 'gemini-1.5-flash'];
    let response: Response | null = null;
    let lastError: Error | null = null;

    for (const modelName of models) {
      try {
        response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${apiKey}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: systemPrompt
              }]
            }],
            generationConfig: {
              temperature: 0.3,
              topK: 40,
              topP: 0.95,
              maxOutputTokens: 300,
            }
          })
        });

        if (response.ok) {
          break; // Success, exit the loop
        } else {
          lastError = new Error(`Model ${modelName} failed with status ${response.status}`);
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(`Unknown error with model ${modelName}`);
        console.log(`Failed to call ${modelName}:`, lastError.message);
      }
    }

    if (!response || !response.ok) {
      throw lastError || new Error('All AI models failed to respond');
    }

    const data = await response.json();
    let aiMessage = data.candidates?.[0]?.content?.parts?.[0]?.text || "I am sorry, I could not understand that. Could you please try again?";
    
    // Filter out inappropriate AI responses
    if (aiMessage.toLowerCase().includes('large language model') || 
        aiMessage.toLowerCase().includes('i am an ai') ||
        aiMessage.toLowerCase().includes('google ai website') ||
        aiMessage.toLowerCase().includes('as an ai')) {
      aiMessage = "I am here to help you create your CV. Let us focus on getting your information ready for your job applications.";
    }
    
    // Clean up any remaining formatting
    aiMessage = aiMessage.replace(/^```json\s*/, '').replace(/\s*```$/, '').replace(/^```\s*/, '').replace(/\s*```$/, '');

    // Extract user profile data from the input
    const extractedProfile = extractUserProfile(userInput, conversationState, currentProfile);

    // Determine next state based on completeness
    const nextState = getNextConversationState(conversationState, extractedProfile, currentProfile);

    console.log('Final response - extractedProfile:', extractedProfile);
    console.log('Final response - nextState:', nextState);

    return new Response(JSON.stringify({
      message: aiMessage,
      userProfile: extractedProfile,
      nextState: nextState
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in gemini-chat function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
