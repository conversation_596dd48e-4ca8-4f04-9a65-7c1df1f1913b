
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { userInput, conversationState, currentProfile } = await req.json();
    
    const apiKey = Deno.env.get('GEMINI_API_KEY');
    if (!apiKey) {
      throw new Error('Gemini API key not configured');
    }

    // Create context-aware prompts
    const systemPrompt = `You are a helpful CV assistant for hospitality workers in Germany. 
    - Always stay in character as a CV helper
    - Never mention you are an AI, large language model, or refer to Google AI
    - Focus only on collecting CV information
    - Use simple, clear language suitable for hospitality workers
    - Ask one question at a time and don't repeat questions
    - Validate user responses thoroughly
    - If someone asks unrelated questions, politely redirect them back to CV building
    - Do not ask for things you already have in the profile
    
    Current conversation state: ${conversationState}
    User input: ${userInput}`;

    // Try Gemini 2.0 Flash first, fallback to 1.5 Flash
    let modelName = 'gemini-2.0-flash-exp';
    let response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: systemPrompt
          }]
        }],
        generationConfig: {
          temperature: 0.3,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 200,
        }
      })
    });

    // If 2.0 fails, try 1.5 Flash
    if (!response.ok) {
      modelName = 'gemini-1.5-flash';
      response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: systemPrompt
            }]
          }],
          generationConfig: {
            temperature: 0.3,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 200,
          }
        })
      });
    }

    if (!response.ok) {
      throw new Error('Failed to get response from Gemini');
    }

    const data = await response.json();
    let aiMessage = data.candidates?.[0]?.content?.parts?.[0]?.text || "I am sorry, I could not understand that. Could you please try again?";
    
    // Filter out inappropriate AI responses
    if (aiMessage.toLowerCase().includes('large language model') || 
        aiMessage.toLowerCase().includes('i am an ai') ||
        aiMessage.toLowerCase().includes('google ai website') ||
        aiMessage.toLowerCase().includes('as an ai')) {
      aiMessage = "I am here to help you create your CV. Let us focus on getting your information ready for your job applications.";
    }
    
    // Clean up any remaining formatting
    aiMessage = aiMessage.replace(/^```json\s*/, '').replace(/\s*```$/, '').replace(/^```\s*/, '').replace(/\s*```$/, '');

    return new Response(JSON.stringify({ message: aiMessage }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in gemini-chat function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
