
import React, { createContext, useContext, useState, ReactNode } from 'react';

interface LanguageContextType {
  language: 'en' | 'de';
  setLanguage: (lang: 'en' | 'de') => void;
  t: (key: string) => string;
}

const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.jobs': 'Find Jobs',
    'nav.cv': 'CV Builder',
    'nav.language': 'Language',

    // Hero Section
    'hero.title': 'Your true potential, instantly seen.',
    'hero.title.highlight': 'Get hired faster.',
    'hero.subtitle': 'AI-Powered Job Matching for Blue-Collar Workers',
    'hero.description': 'With Sohnus, our AI assistant supports you every step of the way — from creating your profile and CV to finding the right job in Germany.',
    'hero.description.tagline': 'Your true potential, instantly seen. Get hired faster.',
    'hero.cta.primary': 'Start Job Search',

    // How It Works
    'how.title': 'How It Works',
    'how.step1.title': 'Tell Us About Yourself',
    'how.step1.description': 'Share your experience, skills, and job preferences with our AI assistant',
    'how.step2.title': 'AI Creates Your CV',
    'how.step2.description': 'Our AI generates a professional CV tailored to hospitality jobs in Germany',
    'how.step3.title': 'Find Perfect Matches',
    'how.step3.description': 'Get matched with the best job opportunities based on your profile',
    'how.step4.title': 'Apply Instantly',
    'how.step4.description': 'Send your application with AI-generated cover letters to multiple employers',

    // Chat Interface
    'chat.title': 'AI Job Assistant',
    'chat.subtitle': 'Let me help you create your perfect CV',
    'chat.welcome.role.selection': 'Hello! I\'m here to help you find the perfect hospitality job in Germany. First, tell me - what role are you interested in? For example: waiter, chef, bartender, kitchen assistant, hotel receptionist, cleaner, etc.',
    'chat.placeholder': 'Type your message here...',
    'chat.error': 'Sorry, there was an error processing your request. Please try again.',
    'chat.cv.ready': 'CV Ready!',
    'chat.cv.ready.description': 'Your professional CV has been created and is ready for download.',
    'chat.api.warning': 'Please set your Gemini API key to enable real AI conversation.',

    // CV Sections
    'cv.preview.title': 'CV Preview',
    'cv.preview.template': '',
    'cv.preview.empty': 'Start chatting to create your CV',
    'cv.download': 'Download PDF',
    'cv.findJobs': 'Find Jobs',
    'cv.download.success': 'CV Downloaded',
    'cv.download.success.description': 'Your CV has been successfully downloaded as PDF.',
    'cv.download.error': 'Download Failed',
    'cv.download.error.description': 'There was an error downloading your CV. Please try again.',
    'cv.sections.experience': 'Work Experience',
    'cv.sections.skills': 'Skills',
    'cv.sections.languages': 'Languages',
    'cv.sections.education': 'Education',
    'cv.sections.certifications': 'Certifications',
    'cv.hospitality.professional': 'Hospitality Professional',
    'cv.placeholder.name': 'Your Full Name',
    'cv.placeholder.email': '<EMAIL>',
    'cv.placeholder.phone': '+49 ************',
    'cv.placeholder.address': 'Your Address, City, Germany',
    'cv.placeholder.nationality': 'Your Nationality',
    'cv.placeholder.experience': 'Tell me about your work experience...',
    'cv.placeholder.skills': 'What skills do you have?',
    'cv.placeholder.languages': 'What languages do you speak?',

    // Templates
    'templates.title': 'Choose Your CV Template',
    'templates.subtitle': 'Select a template that best represents your professional style',
    'templates.modern.name': 'Modern',
    'templates.modern.description': 'Clean and contemporary design perfect for young professionals',
    'templates.classic.name': 'Classic',
    'templates.classic.description': 'Traditional format preferred by established companies',
    'templates.professional.name': 'Professional',
    'templates.professional.description': 'Sophisticated layout for senior positions',
    'templates.select': 'Select Template',
    'templates.popular': 'Popular',

    // Job Matching
    'jobmatching.title': 'Find Your Perfect Job',
    'jobmatching.description': 'Let our AI help you create the perfect CV and find matching job opportunities',
    'jobs.title': 'Perfect Job Matches for You',
    'jobs.subtitle': 'Based on your profile, here are the best hospitality jobs in Germany',
    'jobs.match': 'Match',
    'jobs.apply': 'Apply Now',
    'jobs.selected': 'Selected',
    'jobs.location': 'Location',
    'jobs.salary': 'Salary',
    'jobs.type': 'Type',

    // CV Builder
    'cvbuilder.title': 'CV Builder',
    'cvbuilder.description': 'Create your professional CV with AI assistance',
  },
  de: {
    // Navigation
    'nav.home': 'Startseite',
    'nav.jobs': 'Jobs finden',
    'nav.cv': 'Lebenslauf',
    'nav.language': 'Sprache',

    // Hero Section
    'hero.title': 'Dein wahres Potenzial, sofort erkannt.',
    'hero.title.highlight': 'Schneller eingestellt werden.',
    'hero.subtitle': 'KI-gestützte Jobvermittlung für Arbeiter',
    'hero.description': 'Mit Sohnus unterstützt dich unser KI-Assistent bei jedem Schritt — von der Erstellung deines Profils und Lebenslaufs bis zur Suche nach dem passenden Job in Deutschland.',
    'hero.description.tagline': 'Dein wahres Potenzial, sofort erkannt. Schneller eingestellt werden.',
    'hero.cta.primary': 'Jobsuche starten',

    // How It Works
    'how.title': 'So funktioniert es',
    'how.step1.title': 'Erzähle uns von dir',
    'how.step1.description': 'Teile deine Erfahrungen, Fähigkeiten und Jobwünsche mit unserem KI-Assistenten',
    'how.step2.title': 'KI erstellt deinen Lebenslauf',
    'how.step2.description': 'Unsere KI erstellt einen professionellen Lebenslauf für Gastgewerbejobs in Deutschland',
    'how.step3.title': 'Finde perfekte Matches',
    'how.step3.description': 'Erhalte die besten Jobmöglichkeiten basierend auf deinem Profil',
    'how.step4.title': 'Sofort bewerben',
    'how.step4.description': 'Sende deine Bewerbung mit KI-generierten Anschreiben an mehrere Arbeitgeber',

    // Chat Interface
    'chat.title': 'KI-Job-Assistent',
    'chat.subtitle': 'Lass mich dir beim perfekten Lebenslauf helfen',
    'chat.welcome.role.selection': 'Hallo! Ich helfe dir dabei, den perfekten Gastgewerbejob in Deutschland zu finden. Erzähl mir zuerst - welche Position interessiert dich? Zum Beispiel: Kellner, Koch, Barkeeper, Küchenhelfer, Hotelrezeptionist, Reinigungskraft, etc.',
    'chat.placeholder': 'Schreibe deine Nachricht hier...',
    'chat.error': 'Entschuldigung, es gab einen Fehler bei der Bearbeitung. Bitte versuche es erneut.',
    'chat.cv.ready': 'Lebenslauf fertig!',
    'chat.cv.ready.description': 'Dein professioneller Lebenslauf wurde erstellt und ist zum Download bereit.',
    'chat.api.warning': 'Bitte setze deinen Gemini API-Schlüssel, um echte KI-Unterhaltung zu aktivieren.',

    // CV Sections
    'cv.preview.title': 'Lebenslauf-Vorschau',
    'cv.preview.template': '',
    'cv.preview.empty': 'Beginne zu chatten, um deinen Lebenslauf zu erstellen',
    'cv.download': 'PDF herunterladen',
    'cv.findJobs': 'Jobs finden',
    'cv.download.success': 'Lebenslauf heruntergeladen',
    'cv.download.success.description': 'Dein Lebenslauf wurde erfolgreich als PDF heruntergeladen.',
    'cv.download.error': 'Download fehlgeschlagen',
    'cv.download.error.description': 'Fehler beim Herunterladen des Lebenslaufs. Bitte versuche es erneut.',
    'cv.sections.experience': 'Berufserfahrung',
    'cv.sections.skills': 'Fähigkeiten',
    'cv.sections.languages': 'Sprachen',
    'cv.sections.education': 'Bildung',
    'cv.sections.certifications': 'Zertifikate',
    'cv.hospitality.professional': 'Gastgewerbe-Fachkraft',
    'cv.placeholder.name': 'Dein vollständiger Name',
    'cv.placeholder.email': '<EMAIL>',
    'cv.placeholder.phone': '+49 ************',
    'cv.placeholder.address': 'Deine Adresse, Stadt, Deutschland',
    'cv.placeholder.nationality': 'Deine Nationalität',
    'cv.placeholder.experience': 'Erzähle mir von deiner Berufserfahrung...',
    'cv.placeholder.skills': 'Welche Fähigkeiten hast du?',
    'cv.placeholder.languages': 'Welche Sprachen sprichst du?',

    // Templates
    'templates.title': 'Wähle deine Lebenslauf-Vorlage',
    'templates.subtitle': 'Wähle eine Vorlage, die deinen professionellen Stil am besten repräsentiert',
    'templates.modern.name': 'Modern',
    'templates.modern.description': 'Sauberes und zeitgemäßes Design für junge Fachkräfte',
    'templates.classic.name': 'Klassisch',
    'templates.classic.description': 'Traditionelles Format von etablierten Unternehmen bevorzugt',
    'templates.professional.name': 'Professionell',
    'templates.professional.description': 'Elegantes Layout für Führungspositionen',
    'templates.select': 'Vorlage wählen',
    'templates.popular': 'Beliebt',

    // Job Matching
    'jobmatching.title': 'Finde deinen perfekten Job',
    'jobmatching.description': 'Lass unsere KI dir beim perfekten Lebenslauf helfen und passende Jobmöglichkeiten finden',
    'jobs.title': 'Perfekte Job-Matches für dich',
    'jobs.subtitle': 'Basierend auf deinem Profil sind hier die besten Gastgewerbejobs in Deutschland',
    'jobs.match': 'Match',
    'jobs.apply': 'Jetzt bewerben',
    'jobs.selected': 'Ausgewählt',
    'jobs.location': 'Standort',
    'jobs.salary': 'Gehalt',
    'jobs.type': 'Art',

    // CV Builder
    'cvbuilder.title': 'Lebenslauf-Builder',
    'cvbuilder.description': 'Erstelle deinen professionellen Lebenslauf mit KI-Unterstützung',
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Detect browser language and set as default
  const browserLang = navigator.language.startsWith('de') ? 'de' : 'en';
  const [language, setLanguage] = useState<'en' | 'de'>(browserLang);

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations['en']] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
