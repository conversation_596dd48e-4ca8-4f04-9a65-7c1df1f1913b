import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { UserProfile } from '@/types/UserProfile';
import { processUserInput } from '@/services/geminiService';
import { processUserInputViaEdgeFunction } from '@/services/chatService';
import { useLanguage } from '@/contexts/LanguageContext';
import { getTemplateForRole } from '@/services/cvTemplateService';

interface Message {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
}

interface ChatInterfaceProps {
  onProfileUpdate: (profile: UserProfile) => void;
  onChatCompletion: () => void;
  onTemplateSelect: (template: string) => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ onProfileUpdate, onChatCompletion, onTemplateSelect }) => {
  const { t } = useLanguage();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "Hello! I'm here to help you create a professional CV for your hospitality job in Germany. Let's start - what type of hospitality job are you looking for? For example: Chef, Waiter, Bartender, Hotel Reception, or Housekeeper?",
      isUser: false,
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [conversationState, setConversationState] = useState('role_selection');
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [userJobRole, setUserJobRole] = useState('');
  const [accumulatedProfile, setAccumulatedProfile] = useState<UserProfile>({
    personalInfo: { fullName: '', email: '', phone: '', address: '', nationality: '' },
    workExperience: [],
    skills: [],
    languages: [],
    education: [],
    certifications: []
  });
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const determineTemplate = (role: string): string => {
    const lowerRole = role.toLowerCase();
    if (lowerRole.includes('chef') || lowerRole.includes('cook') || lowerRole.includes('kitchen')) {
      return 'professional';
    } else if (lowerRole.includes('waiter') || lowerRole.includes('bartender') || lowerRole.includes('server')) {
      return 'modern';
    } else {
      return 'classic';
    }
  };

  const mergeProfileData = (existing: UserProfile, newData: UserProfile): UserProfile => {
    console.log('Merging profiles - existing:', existing, 'newData:', newData);

    const merged = {
      personalInfo: {
        fullName: newData.personalInfo?.fullName || existing.personalInfo?.fullName || '',
        email: newData.personalInfo?.email || existing.personalInfo?.email || '',
        phone: newData.personalInfo?.phone || existing.personalInfo?.phone || '',
        address: newData.personalInfo?.address || existing.personalInfo?.address || '',
        nationality: newData.personalInfo?.nationality || existing.personalInfo?.nationality || ''
      },
      workExperience: newData.workExperience.length > 0 ? 
        [...existing.workExperience.filter(exp => !newData.workExperience.some(newExp => newExp.jobTitle === exp.jobTitle)), ...newData.workExperience] : existing.workExperience,
      skills: newData.skills.length > 0 ? 
        [...existing.skills.filter(skill => !newData.skills.some(newSkill => newSkill.name === skill.name)), ...newData.skills] : existing.skills,
      languages: newData.languages.length > 0 ? 
        [...existing.languages.filter(lang => !newData.languages.some(newLang => newLang.name === lang.name)), ...newData.languages] : existing.languages,
      education: newData.education.length > 0 ? 
        [...existing.education.filter(edu => !newData.education.some(newEdu => newEdu.degree === edu.degree)), ...newData.education] : existing.education,
      certifications: newData.certifications.length > 0 ?
        [...existing.certifications.filter(cert => !newData.certifications.some(newCert => newCert.name === cert.name)), ...newData.certifications] : existing.certifications
    };

    console.log('Merged result:', merged);
    return merged;
  };

  const isProfileComplete = (profile: UserProfile): boolean => {
    return (
      profile.personalInfo.fullName.length > 0 &&
      profile.personalInfo.email.length > 0 &&
      profile.personalInfo.phone.length > 0 &&
      profile.personalInfo.address.length > 0 &&
      profile.personalInfo.nationality.length > 0 &&
      profile.workExperience.length > 0 &&
      profile.skills.length > 0 &&
      profile.languages.length > 0
    );
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputValue;
    setInputValue('');
    setIsLoading(true);

    try {
      // Handle role selection first
      if (conversationState === 'role_selection') {
        const jobRole = currentInput.toLowerCase();
        const selectedCVTemplate = getTemplateForRole(jobRole);

        setSelectedTemplate(selectedCVTemplate.id);
        setUserJobRole(currentInput);
        onTemplateSelect(selectedCVTemplate.id);

        const botMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: `Perfect! I've selected the "${selectedCVTemplate.name}" template which is ideal for ${currentInput} positions. This template features ${selectedCVTemplate.description.toLowerCase()}. Now let's create your professional CV! What is your full name? Please write your first name and last name.`,
          isUser: false,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, botMessage]);
        setConversationState('personal_info');
        setIsLoading(false);
        return;
      }

      // Try to process with the secure Edge Function first
      try {
        // Get recent conversation history for context
        const conversationHistory = messages.slice(-6).map(msg => ({
          content: msg.content,
          isUser: msg.isUser
        }));

        const response = await processUserInputViaEdgeFunction(currentInput, conversationState, accumulatedProfile, conversationHistory);

        const botMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: response.message,
          isUser: false,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, botMessage]);

        // Use the Edge Function response instead of local logic
        if (response.userProfile && Object.keys(response.userProfile).some(key =>
          (typeof response.userProfile[key] === 'object' && Object.keys(response.userProfile[key]).length > 0) ||
          (Array.isArray(response.userProfile[key]) && response.userProfile[key].length > 0)
        )) {
          const updatedProfile = mergeProfileData(accumulatedProfile, response.userProfile);
          setAccumulatedProfile(updatedProfile);
          onProfileUpdate(updatedProfile);

          console.log('Updated profile from Edge Function:', updatedProfile);
        }

        // Use the Edge Function's next state
        if (response.nextState) {
          console.log('Setting conversation state from Edge Function:', response.nextState);
          setConversationState(response.nextState);

          if (response.nextState === 'completion' && isProfileComplete(accumulatedProfile)) {
            onChatCompletion();
            toast({
              title: "Resume Ready!",
              description: "Your resume is complete and ready for job matching.",
            });
          }
        }
      } catch (edgeFunctionError) {
        console.log('Edge function failed, falling back to direct API call:', edgeFunctionError);
        // Fallback to the original method
        const response = await processUserInput(currentInput, conversationState, accumulatedProfile);
        
        const botMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: response.message,
          isUser: false,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, botMessage]);

        if (response.userProfile) {
          const updatedProfile = mergeProfileData(accumulatedProfile, response.userProfile);
          setAccumulatedProfile(updatedProfile);
          onProfileUpdate(updatedProfile);
          
          if (response.nextState === 'completion' && isProfileComplete(updatedProfile)) {
            onChatCompletion();
            toast({
              title: "Resume Ready!",
              description: "Your resume is complete and ready for job matching.",
            });
          }
        }
        
        if (response.nextState) {
          setConversationState(response.nextState);
        }
      }

    } catch (error) {
      console.error('Error processing message:', error);

      // Provide more helpful error messages based on the error type
      let errorContent = "I'm sorry, there was a problem processing your message. Please try again.";

      if (error instanceof Error) {
        if (error.message.includes('API key')) {
          errorContent = "It looks like there's an issue with the AI service configuration. Please try again in a moment, or contact support if the problem persists.";
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorContent = "I'm having trouble connecting to the AI service. Please check your internet connection and try again.";
        } else if (error.message.includes('rate limit')) {
          errorContent = "The AI service is currently busy. Please wait a moment and try again.";
        }
      }

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: errorContent,
        isUser: false,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);

      // Add a helpful suggestion
      setTimeout(() => {
        const suggestionMessage: Message = {
          id: (Date.now() + 2).toString(),
          content: "You can also try rephrasing your answer or use the reset button to start over if needed.",
          isUser: false,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, suggestionMessage]);
      }, 1000);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const resetChat = () => {
    setMessages([{
      id: '1',
      content: "Hello! I'm here to help you create a professional CV for your hospitality job in Germany. Let's start - what type of hospitality job are you looking for? For example: Chef, Waiter, Bartender, Hotel Reception, or Housekeeper?",
      isUser: false,
      timestamp: new Date()
    }]);
    setConversationState('role_selection');
    setSelectedTemplate('');
    setUserJobRole('');
    setAccumulatedProfile({
      personalInfo: { fullName: '', email: '', phone: '', address: '', nationality: '' },
      workExperience: [],
      skills: [],
      languages: [],
      education: [],
      certifications: []
    });
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 h-[600px] flex flex-col">
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-4 rounded-t-xl">
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full flex items-center justify-center text-xl">
              ✨
            </div>
            <div>
              <h3 className="font-semibold">{t('chat.title')}</h3>
              <p className="text-sm opacity-90">{t('chat.subtitle')}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={resetChat}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] p-3 rounded-lg ${
                message.isUser
                  ? 'bg-blue-600 text-white rounded-br-sm text-right'
                  : 'bg-gray-100 text-gray-900 rounded-bl-sm text-left'
              }`}
            >
              <div className="flex items-start gap-2">
                {!message.isUser && (
                  <div className="w-6 h-6 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full flex items-center justify-center text-sm mt-1 flex-shrink-0">
                    ✨
                  </div>
                )}
                <div className="flex-1">
                  <p className="text-sm">{message.content}</p>
                  <p className={`text-xs mt-1 opacity-70`}>
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
                {message.isUser && <User className="w-4 h-4 mt-1 flex-shrink-0" />}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-100 p-3 rounded-lg rounded-bl-sm max-w-[80%]">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full flex items-center justify-center text-sm">
                  ✨
                </div>
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      <div className="p-4 border-t border-gray-200">
        <div className="flex gap-2">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={t('chat.placeholder')}
            disabled={isLoading}
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

// Helper functions that were in the original geminiService
const extractUserProfile = (userInput: string, state: string, currentProfile: any): any => {
  const profile: any = {
    personalInfo: {},
    workExperience: [],
    skills: [],
    languages: [],
    education: [],
    certifications: []
  };

  console.log('Extracting profile for state:', state, 'Input:', userInput);

  switch (state) {
    case 'personal_info':
      if (!currentProfile.personalInfo.fullName) {
        const nameWords = userInput.trim().split(' ').filter(w => w.length > 1);
        if (nameWords.length >= 2) {
          // Capitalize first letter of each word
          const formattedName = nameWords.map(word => 
            word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          ).join(' ');
          profile.personalInfo.fullName = formattedName;
        }
      } else if (!currentProfile.personalInfo.email) {
        const emailMatch = userInput.match(/\S+@\S+\.\S+/);
        if (emailMatch) {
          profile.personalInfo.email = emailMatch[0];
        }
      } else if (!currentProfile.personalInfo.phone) {
        const phoneMatch = userInput.match(/[\+]?[(]?[\d\s\-\(\)]{8,}/);
        if (phoneMatch) {
          profile.personalInfo.phone = phoneMatch[0].trim();
        }
      }
      break;

    case 'contact_details':
      if (!currentProfile.personalInfo.address) {
        // More flexible address validation - check for street name, number, and city
        const hasStreetAndNumber = /\d+/.test(userInput) && userInput.length > 10;
        const hasGermanCity = /berlin|hamburg|münchen|munich|köln|cologne|frankfurt|stuttgart|düsseldorf|dortmund|essen|leipzig|bremen|dresden|hannover|nürnberg|nuremberg/i.test(userInput);
        const hasPostalCode = /\d{5}/.test(userInput);
        const hasStreetKeywords = /str|straße|platz|weg|allee|ring|damm|ufer/i.test(userInput);

        if ((hasStreetAndNumber || hasGermanCity || hasPostalCode || hasStreetKeywords) && userInput.length > 15) {
          profile.personalInfo.address = userInput.trim();
        }
      } else if (!currentProfile.personalInfo.nationality) {
        const nationalities = {
          'china': 'Chinese', 'chinese': 'Chinese', 'german': 'German', 'germany': 'German', 'deutschland': 'German',
          'american': 'American', 'usa': 'American', 'united states': 'American',
          'british': 'British', 'uk': 'British', 'england': 'British',
          'spanish': 'Spanish', 'spain': 'Spanish',
          'french': 'French', 'france': 'French',
          'italian': 'Italian', 'italy': 'Italian',
          'turkish': 'Turkish', 'turkey': 'Turkish',
          'polish': 'Polish', 'poland': 'Polish',
          'ukrainian': 'Ukrainian', 'ukraine': 'Ukrainian',
          'syrian': 'Syrian', 'syria': 'Syrian',
          'afghan': 'Afghan', 'afghanistan': 'Afghan',
          'romanian': 'Romanian', 'romania': 'Romanian',
          'bulgarian': 'Bulgarian', 'bulgaria': 'Bulgarian',
          'indian': 'Indian', 'india': 'Indian',
          'pakistani': 'Pakistani', 'pakistan': 'Pakistani'
        };

        const inputLower = userInput.toLowerCase();
        const foundKey = Object.keys(nationalities).find(key => inputLower.includes(key));
        if (foundKey) {
          profile.personalInfo.nationality = nationalities[foundKey as keyof typeof nationalities];
        }
      }
      break;
      
    case 'work_experience':
      const lowerInput = userInput.toLowerCase();
      
      // Extract job title
      let jobTitle = 'Hospitality Professional';
      if (lowerInput.includes('bartender') || lowerInput.includes('bar tender')) {
        jobTitle = 'Bartender';
      } else if (lowerInput.includes('waiter') || lowerInput.includes('server') || lowerInput.includes('serving')) {
        jobTitle = 'Waiter/Server';
      } else if (lowerInput.includes('chef') || lowerInput.includes('cook')) {
        jobTitle = 'Chef/Cook';
      } else if (lowerInput.includes('reception') || lowerInput.includes('front desk')) {
        jobTitle = 'Hotel Receptionist';
      } else if (lowerInput.includes('housekeeper') || lowerInput.includes('cleaning')) {
        jobTitle = 'Housekeeper';
      }

      // Extract company name
      let company = 'Restaurant/Hotel';
      const companyMatch = userInput.match(/(?:at|in|for)\s+([A-Z][a-zA-Z\s]+(?:Hotel|Restaurant|Bar|Cafe|Resort|Club))/i);
      if (companyMatch) {
        company = companyMatch[1];
      }

      // Extract duration
      let duration = '2020-Present';
      const workYearMatch = userInput.match(/(\d{4})\s*[-to]*\s*(\d{4})?\s*(?:to present|present)?/i);
      if (workYearMatch) {
        duration = workYearMatch[2] ? `${workYearMatch[1]}-${workYearMatch[2]}` : `${workYearMatch[1]}-Present`;
      }
      
      profile.workExperience.push({
        jobTitle,
        company,
        duration,
        description: `Worked as ${jobTitle} providing excellent customer service and maintaining high standards of hospitality.`
      });
      break;
      
    case 'skills':
      const skillKeywords = {
        'Customer Service': ['customer', 'service', 'guest', 'serving'],
        'Communication': ['communication', 'talking', 'speaking', 'language'],
        'Teamwork': ['team', 'cooperation', 'together', 'group'],
        'Bartending': ['cocktail', 'drink', 'bar', 'mixing', 'alcohol'],
        'Cooking': ['cook', 'food', 'kitchen', 'prepare', 'recipe'],
        'Cash Handling': ['money', 'cash', 'payment', 'register', 'till'],
        'Cleaning': ['clean', 'tidy', 'sanitize', 'housekeeping'],
        'Multitasking': ['multitask', 'busy', 'many things', 'quick'],
        'Leadership': ['lead', 'manage', 'supervise', 'boss', 'train']
      };
      
      Object.entries(skillKeywords).forEach(([skill, keywords]) => {
        if (keywords.some(keyword => lowerInput.includes(keyword))) {
          let level = 'Intermediate';
          if (lowerInput.includes('very good') || lowerInput.includes('expert') || lowerInput.includes('excellent')) {
            level = 'Advanced';
          } else if (lowerInput.includes('basic') || lowerInput.includes('learning') || lowerInput.includes('beginner')) {
            level = 'Beginner';
          }
          profile.skills.push({ name: skill, level });
        }
      });
      break;
      
    case 'languages':
      const langKeywords = ['english', 'german', 'turkish', 'arabic', 'spanish', 'french', 'italian', 'polish', 'russian', 'romanian', 'bulgarian', 'chinese', 'mandarin'];
      const levelKeywords = {
        'Basic': ['basic', 'little', 'beginner', 'some'],
        'Conversational': ['good', 'okay', 'conversational', 'intermediate'],
        'Fluent': ['fluent', 'very good', 'excellent', 'very well', 'advanced'],
        'Native': ['native', 'mother tongue', 'first language']
      };

      langKeywords.forEach(lang => {
        if (lowerInput.includes(lang)) {
          let level = 'Conversational';
          Object.entries(levelKeywords).forEach(([levelName, keywords]) => {
            if (keywords.some(keyword => lowerInput.includes(keyword))) {
              level = levelName;
            }
          });
          
          let displayName = lang.charAt(0).toUpperCase() + lang.slice(1);
          if (lang === 'mandarin') displayName = 'Chinese';
          
          profile.languages.push({ 
            name: displayName, 
            level 
          });
        }
      });
      break;
      
    case 'education':
      let degree = 'High School';
      if (lowerInput.includes('university') || lowerInput.includes('bachelor') || lowerInput.includes('master')) {
        degree = 'University Degree';
      } else if (lowerInput.includes('vocational') || lowerInput.includes('training') || lowerInput.includes('apprentice')) {
        degree = 'Vocational Training';
      }

      const educationYearMatch = userInput.match(/(\d{4})/);
      profile.education.push({
        degree,
        institution: 'Educational Institution',
        year: educationYearMatch ? educationYearMatch[1] : '2020'
      });
      break;

    case 'certifications':
      if (!lowerInput.includes('no') && !lowerInput.includes('none')) {
        const certTypes = {
          'Food Safety': ['food safety', 'hygiene', 'haccp'],
          'First Aid': ['first aid', 'cpr', 'medical'],
          'Bartending': ['bartending', 'cocktail', 'drink'],
          'Language': ['language', 'german', 'english']
        };

        Object.entries(certTypes).forEach(([certName, keywords]) => {
          if (keywords.some(keyword => lowerInput.includes(keyword))) {
            profile.certifications.push({
              name: certName + ' Certificate',
              issuer: 'Certification Authority',
              year: new Date().getFullYear().toString()
            });
          }
        });
      }
      break;
  }

  console.log('Extracted profile:', profile);
  return profile;
};

const getNextConversationState = (currentState: string, extractedProfile: any, currentProfile: any): string => {
  switch (currentState) {
    case 'personal_info':
      // Check if we have all personal info
      if (!currentProfile.personalInfo.fullName && extractedProfile.personalInfo.fullName) {
        return 'personal_info'; // Stay to collect email
      }
      if (!currentProfile.personalInfo.email && extractedProfile.personalInfo.email) {
        return 'personal_info'; // Stay to collect phone
      }
      if (!currentProfile.personalInfo.phone && extractedProfile.personalInfo.phone) {
        return 'contact_details'; // Move to address/nationality
      }
      return 'personal_info';
      
    case 'contact_details':
      if (!currentProfile.personalInfo.address && extractedProfile.personalInfo.address) {
        return 'contact_details'; // Stay to collect nationality
      }
      if (!currentProfile.personalInfo.nationality && extractedProfile.personalInfo.nationality) {
        return 'work_experience';
      }
      return 'contact_details';
      
    case 'work_experience':
      return 'skills';
    case 'skills':
      return 'languages';
    case 'languages':
      return 'education';
    case 'education':
      return 'certifications';
    case 'certifications':
      return 'completion';
    default:
      return 'completion';
  }
};

export default ChatInterface;
