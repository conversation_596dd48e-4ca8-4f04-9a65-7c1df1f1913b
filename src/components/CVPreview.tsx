import React, { useState, useRef } from 'react';
import { Download, Eye, FileText, Briefcase, Star, Award, Globe, GraduationCap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { UserProfile } from '@/types/UserProfile';
import { generatePDF } from '@/services/pdfService';
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/contexts/LanguageContext';
import { getTemplateForRole } from '@/services/cvTemplateService';

interface CVPreviewProps {
  userProfile: UserProfile;
  template: string;
  isEnabled: boolean;
  onCVComplete: () => void;
}

const CVPreview: React.FC<CVPreviewProps> = ({ userProfile, template, isEnabled, onCVComplete }) => {
  const { t } = useLanguage();
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);
  const cvRef = useRef<HTMLDivElement>(null);

  const handleDownloadPDF = async () => {
    if (!isEnabled) {
      toast({
        title: "CV Incomplete",
        description: "Please complete your CV information through the chat first.",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    try {
      if (cvRef.current) {
        await generatePDF(cvRef.current, `${userProfile.personalInfo.fullName || 'CV'}`);
      }
      toast({
        title: "Success!",
        description: "Your CV has been downloaded successfully.",
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: "Error",
        description: "Failed to generate PDF. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleFindJobs = () => {
    if (!isEnabled) {
      toast({
        title: "CV Incomplete",
        description: "Please complete your CV information through the chat first.",
        variant: "destructive"
      });
      return;
    }
    
    localStorage.setItem('userProfile', JSON.stringify(userProfile));
    onCVComplete();
  };

  const renderCVContent = () => {
    if (!userProfile.personalInfo.fullName) {
      return (
        <div className="text-center py-8">
          <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500">{t('cv.preview.empty')}</p>
        </div>
      );
    }

    // Determine job role from work experience or default
    const jobRole = userProfile.workExperience?.[0]?.jobTitle || template || 'hospitality';
    const selectedTemplate = getTemplateForRole(jobRole);

    // Hospitality Professional Template (Blue Theme)
    if (selectedTemplate.id === 'hospitality-modern' || template === 'modern') {
      return (
        <div className="bg-white min-h-[400px] font-sans">
          {/* Elegant Header with Blue Gradient */}
          <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-800 text-white p-8 relative overflow-hidden">
            <div className="absolute top-0 right-0 w-40 h-40 bg-white/10 rounded-full -translate-y-20 translate-x-20"></div>
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-white/10 rounded-full translate-y-16 -translate-x-16"></div>
            <div className="absolute top-1/2 right-1/4 w-6 h-6 bg-white/20 rounded-full"></div>
            <div className="absolute top-1/4 left-1/4 w-4 h-4 bg-white/20 rounded-full"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <span className="text-2xl font-bold">{userProfile.personalInfo.fullName?.charAt(0) || 'H'}</span>
                </div>
                <div>
                  <h1 className="text-3xl font-bold mb-1">{userProfile.personalInfo.fullName}</h1>
                  <p className="text-blue-100 text-lg font-medium">Hospitality Professional</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <span className="text-xs">📧</span>
                    </div>
                    <span className="font-medium">{userProfile.personalInfo.email}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <span className="text-xs">📱</span>
                    </div>
                    <span className="font-medium">{userProfile.personalInfo.phone}</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <span className="text-xs">📍</span>
                    </div>
                    <span className="font-medium">{userProfile.personalInfo.address}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <span className="text-xs">🌍</span>
                    </div>
                    <span className="font-medium">{userProfile.personalInfo.nationality}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Content Section */}
          <div className="p-8 space-y-8">
            {/* Work Experience */}
            {userProfile.workExperience.length > 0 && (
              <div>
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg">
                    <Briefcase className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-800">Work Experience</h2>
                    <p className="text-gray-500 text-sm">Professional hospitality background</p>
                  </div>
                </div>
                <div className="space-y-6">
                  {userProfile.workExperience.map((exp, index) => (
                    <div key={index} className="relative">
                      <div className="bg-white border border-blue-100 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                        <div className="flex items-start gap-4">
                          <div className="w-12 h-12 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span className="text-blue-600 font-bold text-lg">{index + 1}</span>
                          </div>
                          <div className="flex-1">
                            <h3 className="font-bold text-xl text-gray-800 mb-1">{exp.jobTitle}</h3>
                            <p className="text-blue-600 font-semibold text-lg">{exp.company}</p>
                            <p className="text-gray-500 text-sm mb-3">{exp.duration}</p>
                            <p className="text-gray-700 leading-relaxed">{exp.description}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Skills */}
            {userProfile.skills.length > 0 && (
              <div>
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">★</span>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800">Skills & Expertise</h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {userProfile.skills.map((skill, index) => (
                    <div key={index} className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-100">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-800">{skill.name}</span>
                        <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">{skill.level}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Languages */}
            {userProfile.languages.length > 0 && (
              <div>
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">🌍</span>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800">Languages</h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {userProfile.languages.map((lang, index) => (
                    <div key={index} className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border border-green-100">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-800">{lang.name}</span>
                        <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">{lang.level}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Education */}
            {userProfile.education.length > 0 && (
              <div>
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-amber-500 to-orange-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">🎓</span>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800">Education</h2>
                </div>
                <div className="space-y-4">
                  {userProfile.education.map((edu, index) => (
                    <div key={index} className="bg-gradient-to-r from-amber-50 to-orange-50 p-4 rounded-lg border border-amber-100">
                      <h3 className="font-bold text-gray-800">{edu.degree}</h3>
                      <p className="text-amber-600 font-medium">{edu.institution}</p>
                      <p className="text-sm text-gray-500">{edu.year}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Certifications */}
            {userProfile.certifications.length > 0 && (
              <div>
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">🏆</span>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800">Certifications</h2>
                </div>
                <div className="space-y-4">
                  {userProfile.certifications.map((cert, index) => (
                    <div key={index} className="bg-gradient-to-r from-red-50 to-pink-50 p-4 rounded-lg border border-red-100">
                      <h3 className="font-bold text-gray-800">{cert.name}</h3>
                      <p className="text-red-600 font-medium">{cert.issuer}</p>
                      <p className="text-sm text-gray-500">{cert.year}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      );
    }

    // Culinary Creative Template (Orange Theme)
    if (selectedTemplate.id === 'culinary-creative' || (template === 'modern' && jobRole.toLowerCase().includes('chef'))) {
      return (
        <div className="bg-white min-h-[400px] font-sans">
          {/* Creative Header with Orange Theme */}
          <div className="bg-gradient-to-r from-orange-500 via-red-500 to-orange-600 text-white p-8 relative overflow-hidden">
            <div className="absolute top-0 right-0 w-48 h-48 bg-white/10 rounded-full -translate-y-24 translate-x-24"></div>
            <div className="absolute bottom-0 left-0 w-36 h-36 bg-white/10 rounded-full translate-y-18 -translate-x-18"></div>
            <div className="absolute top-4 right-8 text-6xl opacity-20">👨‍🍳</div>
            <div className="absolute bottom-4 left-8 text-4xl opacity-20">🍽️</div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <span className="text-2xl">👨‍🍳</span>
                </div>
                <div>
                  <h1 className="text-3xl font-bold mb-1">{userProfile.personalInfo.fullName}</h1>
                  <p className="text-orange-100 text-lg font-medium">Culinary Professional</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <span className="text-xs">📧</span>
                    </div>
                    <span className="font-medium">{userProfile.personalInfo.email}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <span className="text-xs">📱</span>
                    </div>
                    <span className="font-medium">{userProfile.personalInfo.phone}</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <span className="text-xs">📍</span>
                    </div>
                    <span className="font-medium">{userProfile.personalInfo.address}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <span className="text-xs">🌍</span>
                    </div>
                    <span className="font-medium">{userProfile.personalInfo.nationality}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Content Section */}
          <div className="p-8 space-y-8">
            {/* Work Experience */}
            {userProfile.workExperience.length > 0 && (
              <div>
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg">
                    <span className="text-white text-xl">🍳</span>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-800">Culinary Experience</h2>
                    <p className="text-gray-500 text-sm">Professional kitchen background</p>
                  </div>
                </div>
                <div className="space-y-6">
                  {userProfile.workExperience.map((exp, index) => (
                    <div key={index} className="relative">
                      <div className="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-xl p-6 shadow-sm">
                        <div className="flex items-start gap-4">
                          <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span className="text-white font-bold text-lg">{index + 1}</span>
                          </div>
                          <div className="flex-1">
                            <h3 className="font-bold text-xl text-gray-800 mb-1">{exp.jobTitle}</h3>
                            <p className="text-orange-600 font-semibold text-lg">{exp.company}</p>
                            <p className="text-gray-500 text-sm mb-3">{exp.duration}</p>
                            <p className="text-gray-700 leading-relaxed">{exp.description}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Skills with culinary focus */}
            {userProfile.skills.length > 0 && (
              <div>
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
                    <Star className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-800">Culinary Skills</h2>
                    <p className="text-gray-500 text-sm">Kitchen expertise & techniques</p>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {userProfile.skills.map((skill, index) => (
                    <div key={index} className="bg-gradient-to-r from-red-50 to-pink-50 border border-red-100 p-4 rounded-lg">
                      <div className="flex justify-between items-center">
                        <span className="font-semibold text-gray-800">{skill.name}</span>
                        <span className="text-xs bg-red-100 text-red-700 px-3 py-1 rounded-full font-medium">{skill.level}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Other sections with orange theme... */}
          </div>
        </div>
      );
    }

    // Professional Executive Template
    if (selectedTemplate.id === 'management-executive' || template === 'professional') {
      return (
        <div className="bg-white min-h-[400px] font-sans flex">
          {/* Left Sidebar */}
          <div className="w-1/3 bg-gradient-to-b from-gray-800 to-gray-900 text-white p-6">
            <div className="text-center mb-8">
              <div className="w-24 h-24 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl font-bold">{userProfile.personalInfo.fullName?.charAt(0) || 'U'}</span>
              </div>
              <h1 className="text-xl font-bold mb-2">{userProfile.personalInfo.fullName}</h1>
              <p className="text-gray-300 text-sm">Hospitality Professional</p>
            </div>

            {/* Contact Info */}
            <div className="mb-8">
              <h3 className="text-purple-300 font-bold mb-4 text-sm uppercase tracking-wide">Contact</h3>
              <div className="space-y-3 text-sm">
                <div>
                  <p className="text-gray-300">Email</p>
                  <p className="text-white text-xs">{userProfile.personalInfo.email}</p>
                </div>
                <div>
                  <p className="text-gray-300">Phone</p>
                  <p className="text-white text-xs">{userProfile.personalInfo.phone}</p>
                </div>
                <div>
                  <p className="text-gray-300">Address</p>
                  <p className="text-white text-xs">{userProfile.personalInfo.address}</p>
                </div>
                <div>
                  <p className="text-gray-300">Nationality</p>
                  <p className="text-white text-xs">{userProfile.personalInfo.nationality}</p>
                </div>
              </div>
            </div>

            {/* Skills Sidebar */}
            {userProfile.skills.length > 0 && (
              <div className="mb-8">
                <h3 className="text-purple-300 font-bold mb-4 text-sm uppercase tracking-wide">Skills</h3>
                <div className="space-y-3">
                  {userProfile.skills.slice(0, 6).map((skill, index) => (
                    <div key={index}>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-white text-xs">{skill.name}</span>
                        <span className="text-gray-300 text-xs">{skill.level}</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-purple-400 to-indigo-400 h-2 rounded-full"
                          style={{
                            width: skill.level === 'Advanced' ? '90%' :
                                   skill.level === 'Intermediate' ? '70%' : '50%'
                          }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Languages Sidebar */}
            {userProfile.languages.length > 0 && (
              <div>
                <h3 className="text-purple-300 font-bold mb-4 text-sm uppercase tracking-wide">Languages</h3>
                <div className="space-y-2">
                  {userProfile.languages.map((lang, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span className="text-white text-xs">{lang.name}</span>
                      <span className="text-gray-300 text-xs">{lang.level}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Right Content */}
          <div className="w-2/3 p-8">
            {/* Work Experience */}
            {userProfile.workExperience.length > 0 && (
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-6 border-b-2 border-purple-500 pb-2">Work Experience</h2>
                <div className="space-y-6">
                  {userProfile.workExperience.map((exp, index) => (
                    <div key={index}>
                      <h3 className="text-lg font-bold text-gray-800">{exp.jobTitle}</h3>
                      <p className="text-purple-600 font-medium">{exp.company}</p>
                      <p className="text-sm text-gray-500 mb-2">{exp.duration}</p>
                      <p className="text-gray-700 text-sm">{exp.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Education */}
            {userProfile.education.length > 0 && (
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-6 border-b-2 border-purple-500 pb-2">Education</h2>
                <div className="space-y-4">
                  {userProfile.education.map((edu, index) => (
                    <div key={index}>
                      <h3 className="text-lg font-bold text-gray-800">{edu.degree}</h3>
                      <p className="text-purple-600 font-medium">{edu.institution}</p>
                      <p className="text-sm text-gray-500">{edu.year}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Certifications */}
            {userProfile.certifications.length > 0 && (
              <div>
                <h2 className="text-2xl font-bold text-gray-800 mb-6 border-b-2 border-purple-500 pb-2">Certifications</h2>
                <div className="space-y-4">
                  {userProfile.certifications.map((cert, index) => (
                    <div key={index}>
                      <h3 className="text-lg font-bold text-gray-800">{cert.name}</h3>
                      <p className="text-purple-600 font-medium">{cert.issuer}</p>
                      <p className="text-sm text-gray-500">{cert.year}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      );
    }

    // Classic Professional Template (improved)
    return (
      <div className="bg-white min-h-[400px] font-serif p-8">
        {/* Header */}
        <div className="border-b-4 border-emerald-600 pb-6 mb-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">{userProfile.personalInfo.fullName}</h1>
            <p className="text-emerald-600 text-lg font-medium mb-4">Hospitality Professional</p>
            <div className="flex justify-center space-x-8 text-sm text-gray-600">
              <span>{userProfile.personalInfo.email}</span>
              <span>{userProfile.personalInfo.phone}</span>
              <span>{userProfile.personalInfo.nationality}</span>
            </div>
            <p className="text-sm text-gray-600 mt-2">{userProfile.personalInfo.address}</p>
          </div>
        </div>

        <div className="space-y-8">
          {/* Work Experience */}
          {userProfile.workExperience.length > 0 && (
            <div>
              <h2 className="text-xl font-bold text-emerald-700 mb-4 uppercase tracking-wide border-b border-emerald-200 pb-2">Work Experience</h2>
              <div className="space-y-4">
                {userProfile.workExperience.map((exp, index) => (
                  <div key={index} className="border-l-4 border-emerald-200 pl-4">
                    <h3 className="font-bold text-gray-800">{exp.jobTitle}</h3>
                    <p className="text-emerald-600 font-medium">{exp.company} | {exp.duration}</p>
                    <p className="text-gray-700 mt-1">{exp.description}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Skills */}
          {userProfile.skills.length > 0 && (
            <div>
              <h2 className="text-xl font-bold text-emerald-700 mb-4 uppercase tracking-wide border-b border-emerald-200 pb-2">Skills</h2>
              <div className="grid grid-cols-2 gap-4">
                {userProfile.skills.map((skill, index) => (
                  <div key={index} className="flex justify-between border-b border-gray-100 pb-2">
                    <span className="font-medium text-gray-800">{skill.name}</span>
                    <span className="text-emerald-600 text-sm">{skill.level}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Languages */}
          {userProfile.languages.length > 0 && (
            <div>
              <h2 className="text-xl font-bold text-emerald-700 mb-4 uppercase tracking-wide border-b border-emerald-200 pb-2">Languages</h2>
              <div className="grid grid-cols-2 gap-4">
                {userProfile.languages.map((lang, index) => (
                  <div key={index} className="flex justify-between border-b border-gray-100 pb-2">
                    <span className="font-medium text-gray-800">{lang.name}</span>
                    <span className="text-emerald-600 text-sm">{lang.level}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Education */}
          {userProfile.education.length > 0 && (
            <div>
              <h2 className="text-xl font-bold text-emerald-700 mb-4 uppercase tracking-wide border-b border-emerald-200 pb-2">Education</h2>
              <div className="space-y-3">
                {userProfile.education.map((edu, index) => (
                  <div key={index}>
                    <h3 className="font-bold text-gray-800">{edu.degree}</h3>
                    <p className="text-emerald-600">{edu.institution} | {edu.year}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Certifications */}
          {userProfile.certifications.length > 0 && (
            <div>
              <h2 className="text-xl font-bold text-emerald-700 mb-4 uppercase tracking-wide border-b border-emerald-200 pb-2">Certifications</h2>
              <div className="space-y-3">
                {userProfile.certifications.map((cert, index) => (
                  <div key={index}>
                    <h3 className="font-bold text-gray-800">{cert.name}</h3>
                    <p className="text-emerald-600">{cert.issuer} | {cert.year}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <Card className="h-[600px] flex flex-col">
      <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-4 rounded-t-xl">
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <FileText className="w-4 h-4" />
            </div>
            <div>
              <h3 className="font-semibold">{t('cv.preview.title')}</h3>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div ref={cvRef}>
          {renderCVContent()}
        </div>
      </div>

      <div className="p-4 border-t border-gray-200 space-y-3">
        <Button
          onClick={handleDownloadPDF}
          disabled={!isEnabled || isGenerating}
          className={`w-full ${
            isEnabled 
              ? 'bg-green-600 hover:bg-green-700' 
              : 'bg-gray-300 cursor-not-allowed'
          }`}
        >
          <Download className="w-4 h-4 mr-2" />
          {isGenerating ? t('cv.generating') : t('cv.download')}
        </Button>
        
        <Button
          onClick={handleFindJobs}
          disabled={!isEnabled}
          className={`w-full ${
            isEnabled 
              ? 'bg-blue-600 hover:bg-blue-700' 
              : 'bg-gray-300 cursor-not-allowed'
          }`}
        >
          <Briefcase className="w-4 h-4 mr-2" />
          {t('cv.findJobs')}
        </Button>
      </div>
    </Card>
  );
};

export default CVPreview;
